import React, { useState, useEffect } from "react";
import { IoCloseOutline } from "react-icons/io5";
import { InteractiveButton } from "Components/InteractiveButton";
import MkdSDK from "Utils/MkdSDK";

const sdk = new MkdSDK();

/**
 * Modal for confirming sport/type/subtype changes on a clinic
 *
 * @param {function} onClose - Function to close the modal
 * @param {function} onConfirm - Function to confirm the action with selected option
 * @param {object} clinic - Clinic object containing sport_id, type, subtype
 * @param {object} eventCounts - Object containing event counts for the clinic (optional, will be fetched if not provided)
 */
const SportChangeModal = ({
  onClose,
  onConfirm,
  clinic,
  eventCounts: propEventCounts,
}) => {
  const [selectedOption, setSelectedOption] = useState("");
  const [eventCounts, setEventCounts] = useState(
    propEventCounts || {
      total: 0,
      completed: 0,
      upcoming: "No upcoming events",
      lastEvent: "No events scheduled",
    }
  );
  const [loadingStats, setLoadingStats] = useState(false);

  // Fetch affected reservations stats
  const fetchAffectedReservationsStats = async () => {
    if (!clinic || !clinic.sport_id || !clinic.type || !clinic.sub_type) {
      return;
    }

    setLoadingStats(true);
    try {
      const response = await sdk.callRawAPI(
        "/v3/api/custom/courtmatchup/club/courts/affected-reservations",
        {
          sport: clinic.sport_id,
          type: clinic.type,
          subtype: clinic.sub_type,
        },
        "POST"
      );

      if (response && !response.error) {
        // Use the stats directly from the API response
        setEventCounts({
          total: response.total_affected || 0,
          completed: response.completed_events || 0,
          upcoming:
            response.upcoming_events > 0
              ? `${response.upcoming_events} upcoming events`
              : "No upcoming events",
          lastEvent: response.last_event_date
            ? new Date(response.last_event_date).toLocaleDateString("en-US", {
                month: "long",
                day: "numeric",
                year: "numeric",
              })
            : "No events scheduled",
        });
      }
    } catch (error) {
      console.error("Error fetching affected reservations stats:", error);
      setEventCounts({
        total: 0,
        completed: 0,
        upcoming: "No upcoming events",
        lastEvent: "No events scheduled",
      });
    } finally {
      setLoadingStats(false);
    }
  };

  // Fetch stats when component mounts if eventCounts not provided
  useEffect(() => {
    if (!propEventCounts && clinic) {
      fetchAffectedReservationsStats();
    }
  }, [clinic, propEventCounts]);

  const handleConfirm = () => {
    if (!selectedOption) return;
    onConfirm(selectedOption);
  };

  return (
    <div className="fixed inset-0 z-[99999] flex items-center justify-center ">
      <div className="fixed inset-0 bg-black opacity-50"></div>
      <div className="relative z-50 w-full max-w-2xl rounded-lg bg-white p-6">
        <div className="flex items-center justify-between border-b pb-4">
          <h2 className="text-2xl font-bold">Confirm Updates</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <IoCloseOutline className="h-6 w-6" />
          </button>
        </div>

        <div className="py-4">
          <p className="mb-4 text-base">
            You're changing the Sport / Type / Subtype for this clinic. This
            affects how the clinic connects to scheduled events; time slots,
            availability, and visibility in the scheduler.
          </p>

          <p className="mb-2 font-medium">
            Below are the scheduled events currently tied to this clinic:
          </p>

          {loadingStats ? (
            <div className="mb-6 ml-6 space-y-2">
              <div className="flex items-center gap-2">
                <div className="h-4 w-32 animate-pulse rounded bg-gray-200"></div>
                <div className="h-4 w-8 animate-pulse rounded bg-gray-200"></div>
              </div>
              <div className="flex items-center gap-2">
                <div className="h-4 w-28 animate-pulse rounded bg-gray-200"></div>
                <div className="h-4 w-8 animate-pulse rounded bg-gray-200"></div>
              </div>
              <div className="flex items-center gap-2">
                <div className="h-4 w-36 animate-pulse rounded bg-gray-200"></div>
                <div className="h-4 w-20 animate-pulse rounded bg-gray-200"></div>
              </div>
              <div className="flex items-center gap-2">
                <div className="h-4 w-32 animate-pulse rounded bg-gray-200"></div>
                <div className="h-4 w-20 animate-pulse rounded bg-gray-200"></div>
              </div>
            </div>
          ) : (
            <div className="mb-6 ml-6">
              <p>Total Scheduled Events: {eventCounts.total}</p>
              <p>Completed Events: {eventCounts.completed}</p>
              <p>Upcoming Events: {eventCounts.upcoming}</p>
              <p>Last Event Date: {eventCounts.lastEvent}</p>
            </div>
          )}

          <p className="mb-4 font-medium">
            Choose how to handle scheduled events:
          </p>

          <div className="space-y-4">
            <div className="flex items-start">
              <input
                type="radio"
                id="option1"
                name="sportChangeOption"
                value="1"
                checked={selectedOption === "1"}
                onChange={(e) => setSelectedOption(e.target.value)}
                className="mt-1 h-4 w-4"
              />
              <div className="ml-2">
                <label htmlFor="option1" className="font-medium">
                  Cancel All Future Events
                </label>
                <p className="text-sm text-gray-600">
                  Delete all upcoming events for this clinic. Past events will
                  remain unchanged.
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <input
                type="radio"
                id="option2"
                name="sportChangeOption"
                value="2"
                checked={selectedOption === "2"}
                onChange={(e) => setSelectedOption(e.target.value)}
                className="mt-1 h-4 w-4"
              />
              <div className="ml-2">
                <label htmlFor="option2" className="font-medium">
                  Apply Changes Only to Future Events
                </label>
                <p className="text-sm text-gray-600">
                  Keep past events intact: Update sport/type/subtype on upcoming
                  events only.
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <input
                type="radio"
                id="option3"
                name="sportChangeOption"
                value="3"
                checked={selectedOption === "3"}
                onChange={(e) => setSelectedOption(e.target.value)}
                className="mt-1 h-4 w-4"
              />
              <div className="ml-2">
                <label htmlFor="option3" className="font-medium">
                  Apply Changes to All Events (Past and Future)
                </label>
                <p className="text-sm text-gray-600">
                  Retroactively apply sport/type/subtype changes to all events
                  connected to this clinic, including completed ones.
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <input
                type="radio"
                id="option4"
                name="sportChangeOption"
                value="4"
                checked={selectedOption === "4"}
                onChange={(e) => setSelectedOption(e.target.value)}
                className="mt-1 h-4 w-4"
              />
              <div className="ml-2">
                <label htmlFor="option4" className="font-medium">
                  Clone This Clinic and Keep Existing Schedule
                </label>
                <p className="text-sm text-gray-600">
                  Keep the current events tied to this clinic, and create a new
                  clinic with your changes that you can schedule separately.
                </p>
              </div>
            </div>

            <div className="flex items-start">
              <input
                type="radio"
                id="option0"
                name="sportChangeOption"
                value="0"
                checked={selectedOption === "0"}
                onChange={(e) => setSelectedOption(e.target.value)}
                className="mt-1 h-4 w-4"
              />
              <div className="ml-2">
                <label htmlFor="option0" className="font-medium">
                  Don't Apply Sport/Type/Subtype Changes
                </label>
                <p className="text-sm text-gray-600">
                  Save all other updates to this clinic, but leave the original
                  sport, type, and subtype unchanged.
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="flex justify-end gap-3 border-t pt-4">
          <button
            onClick={onClose}
            className="rounded-lg border border-gray-300 bg-white px-6 py-2 text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <InteractiveButton
            onClick={handleConfirm}
            className="rounded-lg bg-blue-500 px-6 py-2 text-white hover:bg-blue-600"
            disabled={!selectedOption}
          >
            Confirm and Save Changes
          </InteractiveButton>
        </div>
      </div>
    </div>
  );
};

export default SportChangeModal;
