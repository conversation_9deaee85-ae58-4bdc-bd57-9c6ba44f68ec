import React, { useState, useEffect, useContext } from "react";
import MkdSDK from "Utils/MkdSDK";
import { getManyByIds, GlobalContext, showToast } from "Context/Global";
import { fCurrency } from "Utils/formatNumber";
import { InteractiveButton } from "Components/InteractiveButton";
import LoadingSpinner from "Components/LoadingSpinner";
import { convertTo12Hour, generateTimeOptions } from "Utils/utils";
import { useClub } from "Context/Club";
import Select from "react-select";
import { useForm } from "react-hook-form";
import SportChangeModal from "./SportChangeModal";
import RightSideModal from "Components/RightSideModal";
import { AuthContext } from "Context/Auth";
import CoachSelectionModal from "Components/CoachSelectionModal";
import { XMarkIcon } from "@heroicons/react/24/outline";

const timeOptions = generateTimeOptions();

let sdk = new MkdSDK();
const ClinicDetailsModal = ({ clinic, onClose, getData, isOpen, sports }) => {
  const defaultClinic = clinic || {
    sport_id: "",
    type: "",
    sub_type: "",
    date: "",
    end_date: "",
    start_time: "",
    end_time: "",
    name: "",
    cost_per_head: "",
    description: "",
    recurring: 0,
    id: null,
  };

  // Form control - always initialize with default values
  const { setValue, watch } = useForm({
    defaultValues: {
      sport_id: defaultClinic.sport_id,
      type: defaultClinic.type,
      sub_type: defaultClinic.sub_type,
      date: defaultClinic.date,
      end_date: defaultClinic.end_date,
      start_time: defaultClinic.start_time,
      end_time: defaultClinic.end_time,
      name: defaultClinic.name,
      cost_per_head: defaultClinic.cost_per_head,
      description: defaultClinic.description,
      recurring: defaultClinic.recurring,
    },
  });

  // Basic state
  const [editLoading, setEditLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [coaches, setCoaches] = useState([]);

  // Coach management state
  const [allCoaches, setAllCoaches] = useState([]);
  const [clinicCoaches, setClinicCoaches] = useState([]);
  const [selectedCoaches, setSelectedCoaches] = useState([]);
  const [isCoachModalOpen, setIsCoachModalOpen] = useState(false);
  const [isHoursModalOpen, setIsHoursModalOpen] = useState(false);
  const [selectedCoachForHours, setSelectedCoachForHours] = useState(null);
  const [isSavingHours, setIsSavingHours] = useState(false);
  const [tempSelectedCoaches, setTempSelectedCoaches] = useState([]);
  const [isLoadingCoaches, setIsLoadingCoaches] = useState(false);
  const [isAddingCoach, setIsAddingCoach] = useState(false);
  const [pendingCoachesForHours, setPendingCoachesForHours] = useState([]);
  const [currentCoachIndex, setCurrentCoachIndex] = useState(0);

  // Sport change modal state
  const [showSportChangeModal, setShowSportChangeModal] = useState(false);
  const [originalSportData, setOriginalSportData] = useState({
    sport_id: "",
    type: "",
    sub_type: "",
  });
  const [sportChangeOption, setSportChangeOption] = useState(null);
  const [pendingFormData, setPendingFormData] = useState(null);

  // Sport, type, subtype state
  const [selectedSportTypes, setSelectedSportTypes] = useState([]);
  const [selectedSubTypes, setSelectedSubTypes] = useState([]);
  const [filteredEndTimeOptions, setFilteredEndTimeOptions] =
    useState(timeOptions);

  // Watch form values
  const watchSportId = watch("sport_id");
  const watchSportType = watch("type");
  const watchStartTime = watch("start_time");

  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const { dispatch: authDispatch } = useContext(AuthContext);
  const { club } = useClub();

  // Initialize form values when clinic changes
  useEffect(() => {
    if (clinic) {
      setValue("sport_id", clinic.sport_id || "");
      setValue("type", clinic.type || "");
      setValue("sub_type", clinic.sub_type || "");
      setValue("date", clinic.date || "");
      setValue("end_date", clinic.end_date || "");
      setValue("start_time", clinic.start_time || "");
      setValue("end_time", clinic.end_time || "");
      setValue("name", clinic.name || "");
      setValue("cost_per_head", clinic.cost_per_head || "");
      setValue("description", clinic.description || "");
      setValue("recurring", clinic.recurring || 0);

      // Store original sport data for comparison
      setOriginalSportData({
        sport_id: clinic.sport_id || "",
        type: clinic.type || "",
        sub_type: clinic.sub_type || "",
      });
    }
  }, [clinic, setValue]);

  // Define getCoaches function before using it in useEffect
  const getCoaches = async () => {
    if (!defaultClinic.id) return [];

    setIsLoading(true);
    try {
      // Fetch clinic coaches with their hours
      sdk.setTable("clinic_coaches");
      const clinicCoachesResponse = await sdk.callRestAPI(
        { filter: [`clinic_id,eq,${defaultClinic.id}`] },
        "GETALL"
      );

      // Get coach details for clinic coaches
      const coachesResponse = await getManyByIds(
        globalDispatch,
        authDispatch,
        "coach",
        clinicCoachesResponse?.list.map((coach) => coach.coach_id) || [],
        "user|user_id"
      );

      // Merge clinic coach data with coach details and parse hours
      const coachesWithHours = coachesResponse.list.map((coach) => {
        const clinicCoach = clinicCoachesResponse.list.find(
          (cc) => cc.coach_id === coach.id
        );
        const coachData = clinicCoach
          ? JSON.parse(clinicCoach.data || "{}")
          : {};
        const workingHours = coachData.working_hours || [];

        // Debug log to see the actual data
        console.log("Coach working hours data:", {
          coachId: coach.id,
          coachName: `${coach.user?.first_name} ${coach.user?.last_name}`,
          workingHours,
          coachData,
        });

        // Convert 24-hour format to 12-hour format for display
        const formattedHours = workingHours.map((hour) => {
          // Handle different time formats that might come from the database
          let timeStr = hour;
          if (typeof hour !== "string") {
            timeStr = String(hour);
          }

          // Remove seconds if present (e.g., "09:00:00" -> "09:00")
          timeStr = timeStr.split(":").slice(0, 2).join(":");

          const timeParts = timeStr.split(":");
          if (timeParts.length !== 2) {
            console.warn("Invalid time format:", hour);
            return hour; // Return original if can't parse
          }

          const hours = parseInt(timeParts[0]) || 0;
          const minutes = parseInt(timeParts[1]) || 0;
          const period = hours >= 12 ? "PM" : "AM";
          const hour12 = hours % 12 || 12;
          return `${hour12}:${minutes.toString().padStart(2, "0")} ${period}`;
        });

        return {
          ...coach,
          hours: formattedHours,
          clinicCoachId: clinicCoach?.id,
          clinicCoachData: coachData,
        };
      });

      setCoaches(coachesWithHours);
      setClinicCoaches(clinicCoachesResponse.list || []);
      setSelectedCoaches(coachesWithHours);
    } catch (error) {
      console.log(error);
      return [];
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch all available coaches for the club
  const getAllCoaches = async () => {
    console.log("getAllCoaches called, club:", club);
    if (!club?.id) {
      console.log("No club ID available");
      return;
    }

    setIsLoadingCoaches(true);
    try {
      console.log("Fetching coaches for club ID:", club.id);
      sdk.setTable("coach");
      const response = await sdk.callRestAPI(
        {
          filter: [`courtmatchup_coach.club_id,eq,${club.id}`],
          join: ["user|user_id"],
        },
        "GETALL"
      );
      console.log("Coaches API response:", response);
      setAllCoaches(response.list || []);
    } catch (error) {
      console.log("Error fetching all coaches:", error);
    } finally {
      setIsLoadingCoaches(false);
    }
  };

  // Fetch coaches when clinic ID changes
  useEffect(() => {
    if (clinic?.id) {
      getCoaches();
    }
  }, [clinic?.id]); // Only re-fetch coaches when clinic ID changes, not on every clinic prop change

  // Fetch all available coaches when club changes
  useEffect(() => {
    if (club?.id) {
      getAllCoaches();
    }
  }, [club?.id]);

  // Auto-start hours selection when coaches are pending or index changes
  useEffect(() => {
    if (
      pendingCoachesForHours.length > 0 &&
      !isHoursModalOpen &&
      currentCoachIndex < pendingCoachesForHours.length
    ) {
      console.log(
        "Starting hours selection for coaches:",
        pendingCoachesForHours,
        "Current index:",
        currentCoachIndex
      );
      processCoachesWithHours();
    }
  }, [pendingCoachesForHours.length, currentCoachIndex, isHoursModalOpen]);

  // Cleanup coach selection state when main modal closes
  useEffect(() => {
    if (!isOpen) {
      resetCoachSelectionState();
    }
  }, [isOpen]);

  // Update available types when sport changes
  useEffect(() => {
    if (watchSportId) {
      const selectedSport = sports.find(
        (sport) => sport.id.toString() === watchSportId.toString()
      );
      if (selectedSport) {
        // Filter out empty types (where type is an empty string)
        const validSportTypes =
          selectedSport.sport_types?.filter(
            (sportType) => sportType.type !== ""
          ) || [];
        setSelectedSportTypes(validSportTypes);
      } else {
        setSelectedSportTypes([]);
      }
    } else {
      setSelectedSportTypes([]);
    }
  }, [watchSportId, sports]);

  // Update available subtypes when type changes
  useEffect(() => {
    if (watchSportType) {
      const selectedType = selectedSportTypes.find(
        (type) => type.type === watchSportType
      );
      if (selectedType) {
        // Filter out empty subtypes
        const validSubtypes = (selectedType.subtype || []).filter(
          (subtype) => subtype !== ""
        );
        setSelectedSubTypes(validSubtypes);
      } else {
        setSelectedSubTypes([]);
      }
    } else {
      setSelectedSubTypes([]);
    }
  }, [watchSportType, selectedSportTypes]);

  // Function to filter time options based on start time
  const getFilteredTimeOptions = (startTime) => {
    if (!startTime) return timeOptions;

    // Find the index of the selected start time
    const startTimeIndex = timeOptions.findIndex(
      (option) => option.value === startTime
    );

    if (startTimeIndex === -1) return timeOptions;

    // Return only options that come after the selected start time
    return timeOptions.filter((_, index) => index > startTimeIndex);
  };

  // Update end time options when start time changes
  useEffect(() => {
    if (watchStartTime) {
      // Filter end time options to only show times after the selected start time
      const filteredOptions = getFilteredTimeOptions(watchStartTime);
      setFilteredEndTimeOptions(filteredOptions);
    } else {
      // If no start time is selected, show all options
      setFilteredEndTimeOptions(timeOptions);
    }
  }, [watchStartTime]);

  // Removed the conditional return that was here

  const refreshCoachesData = async () => {
    await getCoaches();
  };

  // Reset coach selection state
  const resetCoachSelectionState = () => {
    setPendingCoachesForHours([]);
    setCurrentCoachIndex(0);
    setTempSelectedCoaches([]);
    setIsCoachModalOpen(false);
    setIsHoursModalOpen(false);
  };

  // Add coach to clinic with hours
  const handleAddCoachWithHours = async (coachToAdd, workingHours) => {
    console.log(
      "handleAddCoachWithHours called with:",
      coachToAdd,
      workingHours
    );

    if (!coachToAdd || !coachToAdd.id) {
      console.error("Invalid coach object:", coachToAdd);
      showToast(globalDispatch, "Invalid coach data", 3000, "error");
      return;
    }

    setIsAddingCoach(true);
    try {
      // Create clinic coach record
      sdk.setTable("clinic_coaches");
      const clinicCoachData = {
        clinic_id: defaultClinic.id,
        coach_id: coachToAdd.id,
        data: JSON.stringify({
          working_hours: workingHours,
          fees: defaultClinic.cost_per_head || 0,
          sport_id: defaultClinic.sport_id,
          type: defaultClinic.type || "",
          sub_type: defaultClinic.sub_type || "",
        }),
      };

      const response = await sdk.callRestAPI(clinicCoachData, "POST");

      if (!response.error) {
        showToast(globalDispatch, "Coach added successfully", 3000, "success");
        await refreshCoachesData();
        return true;
      }
      return false;
    } catch (error) {
      showToast(globalDispatch, error?.message, 3000, "error");
      console.log(error);
      return false;
    } finally {
      setIsAddingCoach(false);
    }
  };

  // Process coaches after time selection
  const processCoachesWithHours = async () => {
    if (pendingCoachesForHours.length === 0) return;

    const currentCoach = pendingCoachesForHours[currentCoachIndex];
    if (!currentCoach) return;

    // Set the current coach for hours selection
    setSelectedCoachForHours({
      ...currentCoach,
      hours: [], // Start with empty hours
    });
    setIsHoursModalOpen(true);
  };

  // Handle completing hours for current coach
  const handleCoachHoursComplete = async (workingHours) => {
    const currentCoach = pendingCoachesForHours[currentCoachIndex];
    console.log(
      "Completing hours for coach:",
      currentCoach,
      "Hours:",
      workingHours
    );

    // Add the coach with the selected hours
    const success = await handleAddCoachWithHours(currentCoach, workingHours);

    if (success) {
      // Move to next coach or finish
      if (currentCoachIndex < pendingCoachesForHours.length - 1) {
        console.log("Moving to next coach, index:", currentCoachIndex + 1);
        setCurrentCoachIndex(currentCoachIndex + 1);
        // The useEffect will automatically trigger the next coach processing
      } else {
        // All coaches processed
        console.log("All coaches processed successfully");
        setPendingCoachesForHours([]);
        setCurrentCoachIndex(0);
        showToast(
          globalDispatch,
          "All coaches added successfully",
          3000,
          "success"
        );
      }
    }
  };

  // Remove coach from clinic
  const handleRemoveCoach = async (coach) => {
    try {
      sdk.setTable("clinic_coaches");
      await sdk.callRestAPI({ id: coach.clinicCoachId }, "DELETE");

      showToast(globalDispatch, "Coach removed successfully", 3000, "success");
      await refreshCoachesData();
    } catch (error) {
      showToast(globalDispatch, error?.message, 3000, "error");
      console.log(error);
    }
  };

  // Function to handle saving all fields at once
  const handleSaveAll = async () => {
    if (!defaultClinic.id) {
      showToast(
        globalDispatch,
        "Cannot save: no clinic selected",
        3000,
        "error"
      );
      return;
    }

    // Get all form values
    const formData = {
      id: defaultClinic.id,
      name: watch("name"),
      cost_per_head: parseFloat(watch("cost_per_head")),
      description: watch("description"),
      sport_id: watch("sport_id"),
      type: watch("type"),
      sub_type: watch("sub_type"),
      date: watch("date"),
      end_date: watch("end_date"),
      start_time: watch("start_time"),
      end_time: watch("end_time"),
      recurring:
        watch("recurring") === 1 || watch("recurring") === true ? 1 : 0,
    };

    // Check if sport/type/subtype has changed
    const sportChanged =
      formData.sport_id !== originalSportData.sport_id ||
      formData.type !== originalSportData.type ||
      formData.sub_type !== originalSportData.sub_type;

    // If sport/type/subtype changed, show the SportChangeModal first
    if (sportChanged) {
      // Show the modal - it will fetch the stats itself
      setShowSportChangeModal(true);

      // Store the form data to be used after modal confirmation
      setPendingFormData(formData);
      return; // Don't proceed with save until modal is confirmed
    }

    // If no sport changes, proceed with normal save
    await performSave(formData);
  };

  // Sport change option mappings
  const sportChangeOptionMappings = {
    0: "no_changes", // Don't Apply Sport/Type/Subtype Changes
    1: "cancel_future", // Cancel All Future Events
    2: "update_future_only", // Apply Changes Only to Future Events
    3: "update_all_events", // Apply Changes to All Events (Past and Future)
    4: "clone_clinic", // Clone This Clinic and Keep Existing Schedule
  };

  // Function to perform the actual save operation
  const performSave = async (formData) => {
    setEditLoading(true);
    try {
      // Create a copy of formData to avoid mutating the original
      const apiPayload = { ...formData };

      // If sport/type/subtype changed and we have a selected option, include it in the payload
      if (sportChangeOption !== null) {
        apiPayload.sport_change_option = sportChangeOption;
        apiPayload.sport_change_action =
          sportChangeOptionMappings[sportChangeOption];
      }

      console.log("API Payload being sent:", apiPayload); // Debug log

      sdk.setTable("clinics");
      const response = await sdk.callRestAPI(apiPayload, "PUT");

      if (!response?.error) {
        showToast(
          globalDispatch,
          "Clinic updated successfully",
          3000,
          "success"
        );

        // Update the local clinic data with all new values
        if (clinic) {
          Object.keys(apiPayload).forEach((key) => {
            if (key !== "id") {
              clinic[key] = apiPayload[key];
            }
          });
        }

        // Update original sport data to reflect the new values
        setOriginalSportData({
          sport_id: formData.sport_id,
          type: formData.type,
          sub_type: formData.sub_type,
        });

        // Exit edit mode
        setIsEditing(false);

        // Reset sport change option
        setSportChangeOption(null);

        // Refresh coaches data
        await refreshCoachesData();

        // Refresh the data in the parent component
        getData();
      }
    } catch (error) {
      showToast(
        globalDispatch,
        error?.message || "An error occurred",
        3000,
        "error"
      );
      console.log(error);
    } finally {
      setEditLoading(false);
    }
  };

  // Make sure form values are updated when entering edit mode
  useEffect(() => {
    if (isEditing) {
      // Ensure all form values are set correctly
      setValue("name", defaultClinic.name || "");
      setValue("cost_per_head", defaultClinic.cost_per_head || "");
      setValue("description", defaultClinic.description || "");
      setValue("sport_id", defaultClinic.sport_id || "");
      setValue("type", defaultClinic.type || "");
      setValue("sub_type", defaultClinic.sub_type || "");
      setValue("date", defaultClinic.date || "");
      setValue("end_date", defaultClinic.end_date || "");
      setValue("start_time", defaultClinic.start_time || "");
      setValue("end_time", defaultClinic.end_time || "");
      setValue("recurring", defaultClinic.recurring);
    }
  }, [isEditing, defaultClinic, setValue]);

  // Hours Selection Modal Component
  const HoursSelectionModal = () => {
    const [selectedHours, setSelectedHours] = useState(
      selectedCoachForHours?.hours || []
    );

    // Helper function to convert 24-hour time to comparable number
    const timeToNumber = (time24) => {
      if (!time24) return 0;
      const [hours, minutes] = time24.split(":").map(Number);
      return hours * 60 + minutes;
    };

    // Helper function to format 24-hour time to 12-hour format
    const formatTo12Hour = (time24) => {
      if (!time24) return "";
      const [hours, minutes] = time24.split(":").map(Number);
      const period = hours >= 12 ? "PM" : "AM";
      const hour12 = hours % 12 || 12;
      return `${hour12}:${minutes.toString().padStart(2, "0")} ${period}`;
    };

    const clinicStartTime = formatTo12Hour(defaultClinic.start_time);
    const clinicEndTime = formatTo12Hour(defaultClinic.end_time);

    // Generate array of time slots between start_time and end_time
    const generateTimeSlotsBetween = (start, end) => {
      const slots = [];
      const startMinutes = timeToNumber(start);
      const endMinutes = timeToNumber(end);

      for (let minutes = startMinutes; minutes < endMinutes; minutes += 30) {
        const hours = Math.floor(minutes / 60);
        const mins = minutes % 60;
        const time24 = `${hours.toString().padStart(2, "0")}:${mins
          .toString()
          .padStart(2, "0")}:00`;
        slots.push({
          time24,
          time12: formatTo12Hour(time24),
        });
      }
      return slots;
    };

    const timeSlots = generateTimeSlotsBetween(
      defaultClinic.start_time,
      defaultClinic.end_time
    );

    const toggleTimeSlot = (timeObj) => {
      setSelectedHours((prev) =>
        prev.includes(timeObj.time12)
          ? prev.filter((h) => h !== timeObj.time12)
          : [...prev, timeObj.time12].sort((a, b) => {
              const convertTo24Hour = (time12) => {
                const [time, period] = time12.split(" ");
                const [hours, minutes] = time.split(":").map(Number);
                let hour24 = hours;
                if (period === "PM" && hours !== 12) hour24 += 12;
                if (period === "AM" && hours === 12) hour24 = 0;
                return hour24 * 60 + minutes;
              };
              return convertTo24Hour(a) - convertTo24Hour(b);
            })
      );
    };

    const handleSave = async () => {
      setIsSavingHours(true);
      try {
        // Convert 12-hour format back to 24-hour format for storage
        const workingHours = selectedHours.map((hour) => {
          const [time, period] = hour.split(" ");
          const [hours, minutes] = time.split(":").map(Number);
          let hour24 = hours;
          if (period === "PM" && hours !== 12) hour24 += 12;
          if (period === "AM" && hours === 12) hour24 = 0;
          return `${hour24.toString().padStart(2, "0")}:${minutes
            .toString()
            .padStart(2, "0")}`;
        });

        // Check if this is for adding new coaches or editing existing coach
        if (selectedCoachForHours.clinicCoachId) {
          // Editing existing coach
          const coachData = selectedCoachForHours.clinicCoachData || {};
          coachData.working_hours = workingHours;

          sdk.setTable("clinic_coaches");
          const response = await sdk.callRestAPI(
            {
              id: selectedCoachForHours.clinicCoachId,
              data: JSON.stringify(coachData),
            },
            "PUT"
          );

          if (!response.error) {
            showToast(
              globalDispatch,
              "Coach hours updated successfully",
              3000,
              "success"
            );
            await refreshCoachesData();
            setIsHoursModalOpen(false);
          }
        } else {
          // Adding new coach - use the new flow
          await handleCoachHoursComplete(workingHours);
          setIsHoursModalOpen(false);
        }
      } catch (error) {
        showToast(globalDispatch, error?.message, 3000, "error");
        console.log(error);
      } finally {
        setIsSavingHours(false);
      }
    };

    if (!isHoursModalOpen) return null;

    return (
      <div className="fixed inset-0 z-[99999] flex items-center justify-center bg-black bg-opacity-50">
        <div className="w-full max-w-2xl rounded-lg bg-white shadow-xl">
          {/* Header */}
          <div className="flex items-center justify-between border-b border-gray-200 p-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                Set Coach Hours
              </h3>
              {pendingCoachesForHours.length > 1 && (
                <p className="text-sm text-gray-500">
                  Coach {currentCoachIndex + 1} of{" "}
                  {pendingCoachesForHours.length}
                </p>
              )}
            </div>
            <button
              onClick={resetCoachSelectionState}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          {/* Content */}
          <div className="p-6">
            <div className="flex flex-col space-y-6">
              {/* Coach Info Header */}
              <div className="flex items-center space-x-3 rounded-xl bg-gradient-to-r from-blue-50 to-indigo-50 p-4">
                <div className="h-12 w-12 overflow-hidden rounded-full shadow-sm ring-2 ring-white">
                  <img
                    src={
                      selectedCoachForHours?.user?.photo ||
                      "/default-avatar.png"
                    }
                    alt={`${selectedCoachForHours?.user?.first_name} ${selectedCoachForHours?.user?.last_name}`}
                    className="h-full w-full object-cover"
                  />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">
                    {selectedCoachForHours?.user?.first_name}{" "}
                    {selectedCoachForHours?.user?.last_name}
                  </h3>
                  <p className="text-sm text-gray-600">
                    Clinic: {clinicStartTime} - {clinicEndTime}
                  </p>
                </div>
              </div>

              <div>
                <h4 className="mb-3 font-medium text-gray-900">
                  Available Time Slots
                </h4>
                <div className="grid grid-cols-3 gap-3">
                  {timeSlots.map((timeObj) => (
                    <button
                      key={timeObj.time12}
                      onClick={() => toggleTimeSlot(timeObj)}
                      className={`w-full rounded-lg border-2 p-3 text-center font-medium transition-all duration-200 hover:scale-105
                        ${
                          selectedHours.includes(timeObj.time12)
                            ? "border-primaryBlue bg-primaryBlue text-white shadow-md"
                            : "border-gray-200 text-gray-700 hover:border-primaryBlue hover:bg-blue-50 hover:text-primaryBlue"
                        }
                      `}
                    >
                      {timeObj.time12}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="flex justify-end space-x-3 border-t border-gray-200 p-6">
            <button
              onClick={resetCoachSelectionState}
              className="rounded-lg border border-gray-300 bg-white px-4 py-2 font-medium text-gray-700 transition-colors hover:border-gray-400 hover:bg-gray-50"
            >
              Cancel
            </button>
            <InteractiveButton
              loading={isSavingHours}
              onClick={handleSave}
              className="rounded-lg bg-primaryBlue px-6 py-2 font-medium text-white shadow-sm transition-all hover:bg-primaryBlue/90 hover:shadow-md"
            >
              {isSavingHours ? "Saving..." : "Save Hours"}
            </InteractiveButton>
          </div>
        </div>
      </div>
    );
  };

  // Don't render anything if not open
  if (!isOpen) {
    return null;
  }

  const selectStyles = {
    control: (base) => ({
      ...base,
      borderRadius: "0.5rem",
      border: "none",
      backgroundColor: "#f9fafb",
      "&:hover": {
        border: "none",
        backgroundColor: "#f3f4f6",
      },
      "&:focus-within": {
        border: "none",
        boxShadow: "none",
        backgroundColor: "#f3f4f6",
      },
    }),
    option: (base, state) => ({
      ...base,
      backgroundColor: state.isSelected
        ? "#3b82f6"
        : state.isFocused
        ? "#f3f4f6"
        : "white",
      color: state.isSelected ? "white" : "#374151",
      "&:hover": {
        backgroundColor: state.isSelected ? "#3b82f6" : "#f3f4f6",
      },
    }),
    menu: (base) => ({
      ...base,
      borderRadius: "0.5rem",
      boxShadow:
        "0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",
    }),
    multiValue: (base) => ({
      ...base,
      backgroundColor: "#e5e7eb",
      borderRadius: "0.375rem",
    }),
    multiValueLabel: (base) => ({
      ...base,
      color: "#374151",
      padding: "0.25rem 0.5rem",
    }),
    multiValueRemove: (base) => ({
      ...base,
      color: "#6b7280",
      borderRadius: "0 0.375rem 0.375rem 0",
      "&:hover": {
        backgroundColor: "#d1d5db",
        color: "#374151",
      },
    }),
  };

  return (
    <>
      <RightSideModal
        isOpen={isOpen}
        onClose={onClose}
        title={defaultClinic.name || "Clinic details"}
        showFooter={false}
      >
        <div className="space-y-6">
          {/* Header with clinic info */}
          <div className="rounded-lg border border-blue-100 bg-gradient-to-r from-blue-50 to-indigo-50 p-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  {defaultClinic.name || "Clinic Details"}
                </h3>
                <p className="text-sm text-gray-600">
                  {sports.find((s) => s.id.toString() == defaultClinic.sport_id)
                    ?.name || "No sport selected"}
                  {defaultClinic.type && ` • ${defaultClinic.type}`}
                  {defaultClinic.sub_type && ` • ${defaultClinic.sub_type}`}
                </p>
              </div>
              <div className="text-right">
                <div className="text-lg font-bold text-primaryBlue">
                  {fCurrency(defaultClinic.cost_per_head)}
                </div>
                <div className="text-sm text-gray-500">per person</div>
              </div>
            </div>
          </div>

          {/* Edit/Save/Cancel buttons */}
          <div className="flex justify-end border-b border-gray-200 pb-4">
            {isEditing ? (
              <div className="flex gap-3">
                <button
                  onClick={() => setIsEditing(false)}
                  className="rounded-lg border border-gray-300 bg-white px-4 py-2 font-medium text-gray-700 transition-colors hover:border-gray-400 hover:bg-gray-50"
                >
                  Cancel
                </button>
                <InteractiveButton
                  loading={editLoading}
                  onClick={handleSaveAll}
                  className="rounded-lg bg-primaryBlue px-6 py-2 font-medium text-white shadow-sm transition-all hover:bg-primaryBlue/90 hover:shadow-md"
                >
                  {editLoading ? "Saving..." : "Save All Changes"}
                </InteractiveButton>
              </div>
            ) : (
              <button
                onClick={() => setIsEditing(true)}
                className="rounded-lg bg-primaryBlue px-6 py-2 font-medium text-white shadow-sm transition-all hover:bg-primaryBlue/90 hover:shadow-md"
              >
                <div className="flex items-center space-x-2">
                  <svg
                    className="h-4 w-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                    />
                  </svg>
                  <span>Edit Details</span>
                </div>
              </button>
            )}
          </div>

          {/* Basic Information Section */}
          <div className="space-y-6">
            <div className="border-b border-gray-200 pb-2">
              <h4 className="text-sm font-semibold uppercase tracking-wide text-gray-900">
                Basic Information
              </h4>
            </div>

            {/* Name */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">
                Clinic Name
              </label>
              {isEditing ? (
                <input
                  type="text"
                  value={watch("name") || ""}
                  onChange={(e) => setValue("name", e.target.value)}
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20"
                  placeholder="Enter clinic name"
                />
              ) : (
                <div className="rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900">
                  {defaultClinic.name || "No name provided"}
                </div>
              )}
            </div>

            {/* Cost per person */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">
                Cost per Person
              </label>
              {isEditing ? (
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3">
                    <span className="font-medium text-gray-500">$</span>
                  </div>
                  <input
                    type="number"
                    value={watch("cost_per_head") || ""}
                    onChange={(e) => setValue("cost_per_head", e.target.value)}
                    className="w-full rounded-lg border border-gray-300 py-2 pl-8 pr-3 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20"
                    placeholder="0.00"
                    min="0"
                    step="0.01"
                  />
                </div>
              ) : (
                <div className="rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900">
                  {fCurrency(defaultClinic.cost_per_head)}
                </div>
              )}
            </div>

            {/* Description */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">
                Description
              </label>
              {isEditing ? (
                <textarea
                  value={watch("description") || ""}
                  onChange={(e) => setValue("description", e.target.value)}
                  className="w-full resize-none rounded-lg border border-gray-300 px-3 py-2 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20"
                  rows={3}
                  placeholder="Enter clinic description"
                />
              ) : (
                <div className="min-h-[80px] rounded-lg bg-gray-50 px-3 py-2 text-gray-900">
                  {defaultClinic.description || "No description provided"}
                </div>
              )}
            </div>
          </div>

          {/* Sport Configuration Section */}
          <div className="space-y-6">
            <div className="border-b border-gray-200 pb-2">
              <h4 className="text-sm font-semibold uppercase tracking-wide text-gray-900">
                Sport Configuration
              </h4>
            </div>

            {/* Sport */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">Sport</label>
              {isEditing ? (
                <Select
                  className="w-full text-sm"
                  options={sports
                    .filter((sport) => sport.status === 1)
                    .map((sport) => ({
                      value: sport.id.toString(),
                      label: sport.name,
                    }))}
                  value={{
                    value: watchSportId,
                    label:
                      sports.find((s) => s.id.toString() == watchSportId)
                        ?.name || "Select sport",
                  }}
                  onChange={(option) => {
                    setValue("sport_id", option.value);
                    setValue("type", "");
                    setValue("sub_type", "");
                  }}
                  styles={selectStyles}
                />
              ) : (
                <div className="rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900">
                  {sports.find((s) => s.id.toString() == defaultClinic.sport_id)
                    ?.name || "No sport selected"}
                </div>
              )}
            </div>

            {/* Type */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">Type</label>
              {isEditing ? (
                <>
                  {selectedSportTypes.length > 0 ? (
                    <Select
                      className="w-full text-sm"
                      options={selectedSportTypes.map((type) => ({
                        value: type.type,
                        label: type.type,
                      }))}
                      value={{
                        value: watchSportType,
                        label: watchSportType || "Select type",
                      }}
                      onChange={(option) => {
                        setValue("type", option.value);
                        setValue("sub_type", "");
                      }}
                      styles={selectStyles}
                    />
                  ) : (
                    <div className="rounded-lg bg-gray-100 px-3 py-2 text-sm italic text-gray-500">
                      This sport has no types
                    </div>
                  )}
                </>
              ) : (
                <div className="rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900">
                  {defaultClinic.type || "No type selected"}
                </div>
              )}
            </div>

            {/* Sub-type */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">
                Sub-type
              </label>
              {isEditing ? (
                <>
                  {selectedSubTypes.length > 0 ? (
                    <Select
                      className="w-full text-sm"
                      options={selectedSubTypes.map((subtype) => ({
                        value: subtype,
                        label: subtype,
                      }))}
                      value={{
                        value: watch("sub_type"),
                        label: watch("sub_type") || "Select sub-type",
                      }}
                      onChange={(option) => {
                        setValue("sub_type", option.value);
                      }}
                      styles={selectStyles}
                    />
                  ) : (
                    <div className="rounded-lg bg-gray-100 px-3 py-2 text-sm italic text-gray-500">
                      This type has no sub-types
                    </div>
                  )}
                </>
              ) : (
                <div className="rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900">
                  {defaultClinic.sub_type || "No sub-type selected"}
                </div>
              )}
            </div>
          </div>

          {/* Scheduling Section */}
          <div className="space-y-6">
            <div className="border-b border-gray-200 pb-2">
              <h4 className="text-sm font-semibold uppercase tracking-wide text-gray-900">
                Scheduling
              </h4>
            </div>

            {/* Date Range */}
            <div className="grid grid-cols-2 gap-4">
              {/* Start Date */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  Start Date
                </label>
                {isEditing ? (
                  <input
                    type="date"
                    value={watch("date") || ""}
                    onChange={(e) => setValue("date", e.target.value)}
                    className="w-full rounded-lg border border-gray-300 px-3 py-2 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20"
                  />
                ) : (
                  <div className="rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900">
                    {defaultClinic.date
                      ? new Date(defaultClinic.date).toLocaleDateString(
                          "en-US",
                          {
                            weekday: "long",
                            year: "numeric",
                            month: "long",
                            day: "numeric",
                          }
                        )
                      : "No start date set"}
                  </div>
                )}
              </div>

              {/* End Date */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  End Date
                </label>
                {isEditing ? (
                  <input
                    type="date"
                    value={watch("end_date") || ""}
                    onChange={(e) => setValue("end_date", e.target.value)}
                    className="w-full rounded-lg border border-gray-300 px-3 py-2 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20"
                    min={watch("date") || undefined}
                  />
                ) : (
                  <div className="rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900">
                    {defaultClinic.end_date
                      ? new Date(defaultClinic.end_date).toLocaleDateString(
                          "en-US",
                          {
                            weekday: "long",
                            year: "numeric",
                            month: "long",
                            day: "numeric",
                          }
                        )
                      : "No end date set"}
                  </div>
                )}
              </div>
            </div>

            {/* Time Range */}
            <div className="grid grid-cols-2 gap-4">
              {/* Start time */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  Start Time
                </label>
                {isEditing ? (
                  <Select
                    className="w-full text-sm"
                    options={timeOptions}
                    value={{
                      value: watch("start_time"),
                      label:
                        convertTo12Hour(watch("start_time")) || "Select time",
                    }}
                    onChange={(option) => {
                      setValue("start_time", option.value);
                    }}
                    placeholder="Select start time"
                    styles={selectStyles}
                  />
                ) : (
                  <div className="rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900">
                    {convertTo12Hour(defaultClinic.start_time) || "Not set"}
                  </div>
                )}
              </div>

              {/* End time */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  End Time
                </label>
                {isEditing ? (
                  <Select
                    className="w-full text-sm"
                    options={filteredEndTimeOptions}
                    value={{
                      value: watch("end_time"),
                      label:
                        convertTo12Hour(watch("end_time")) || "Select time",
                    }}
                    onChange={(option) => {
                      setValue("end_time", option.value);
                    }}
                    placeholder={
                      !watchStartTime
                        ? "Select start time first"
                        : "Select end time"
                    }
                    isDisabled={!watchStartTime}
                    styles={selectStyles}
                  />
                ) : (
                  <div className="rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900">
                    {convertTo12Hour(defaultClinic.end_time) || "Not set"}
                  </div>
                )}
              </div>
            </div>

            {/* Recurring */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">
                Recurring Event
              </label>
              {isEditing ? (
                <select
                  value={
                    watch("recurring") === 1 || watch("recurring") === true
                      ? "Yes"
                      : "No"
                  }
                  onChange={(e) =>
                    setValue("recurring", e.target.value === "Yes" ? 1 : 0)
                  }
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 text-gray-900 shadow-sm transition-colors focus:border-primaryBlue focus:outline-none focus:ring-2 focus:ring-primaryBlue/20"
                >
                  <option value="No">No</option>
                  <option value="Yes">Yes</option>
                </select>
              ) : (
                <div className="rounded-lg bg-gray-50 px-3 py-2 font-medium text-gray-900">
                  <div className="flex items-center space-x-2">
                    <div
                      className={`h-2 w-2 rounded-full ${
                        defaultClinic.recurring === 1
                          ? "bg-green-400"
                          : "bg-gray-400"
                      }`}
                    ></div>
                    <span>{defaultClinic.recurring === 1 ? "Yes" : "No"}</span>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Coaches Section */}
          <div className="space-y-6">
            <div className="border-b border-gray-200 pb-2">
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-semibold uppercase tracking-wide text-gray-900">
                  Coaches
                </h4>
                <div className="flex items-center space-x-3">
                  <span className="rounded-full bg-primaryBlue/10 px-2 py-1 text-xs font-semibold text-primaryBlue">
                    {coaches.length} assigned
                  </span>
                  {isEditing && (
                    <button
                      onClick={async () => {
                        await getAllCoaches(); // Refresh coaches before opening modal
                        setIsCoachModalOpen(true);
                      }}
                      disabled={isLoadingCoaches}
                      className="inline-flex items-center rounded-lg bg-primaryBlue px-3 py-1 text-xs text-white transition-colors hover:bg-primaryBlue/90 disabled:cursor-not-allowed disabled:opacity-50"
                    >
                      {isLoadingCoaches ? (
                        <svg
                          className="mr-1 h-3 w-3 animate-spin"
                          fill="none"
                          viewBox="0 0 24 24"
                        >
                          <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"
                          ></circle>
                          <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          ></path>
                        </svg>
                      ) : (
                        <svg
                          className="mr-1 h-3 w-3"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 4v16m8-8H4"
                          />
                        </svg>
                      )}
                      {isLoadingCoaches ? "Loading..." : "Add Coach"}
                    </button>
                  )}
                </div>
              </div>
            </div>

            {coaches.length > 0 ? (
              <div className="space-y-3">
                {coaches.map((coach) => (
                  <div
                    key={coach.id}
                    className="flex items-center justify-between rounded-lg border border-gray-200 bg-gray-50 p-4 transition-all hover:shadow-sm"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="h-10 w-10 overflow-hidden rounded-full shadow-sm ring-2 ring-white">
                        <img
                          src={
                            coach.user?.photo ||
                            coach.photo ||
                            "/default-avatar.png"
                          }
                          alt={`${coach.user?.first_name || ""} ${
                            coach.user?.last_name || ""
                          }`}
                          className="h-full w-full object-cover"
                          onError={(e) => {
                            e.target.src = "/default-avatar.png";
                          }}
                        />
                      </div>
                      <div className="flex flex-col">
                        <div className="font-semibold text-gray-900">
                          {coach.user?.first_name} {coach.user?.last_name}
                        </div>
                        <div className="text-sm text-gray-600">
                          {coach.hours.length > 0
                            ? (() => {
                                // Sort hours by actual time order, not alphabetically
                                const sortedHours = [...coach.hours].sort(
                                  (a, b) => {
                                    // Convert 12-hour format to 24-hour for proper sorting
                                    const convertTo24Hour = (time12) => {
                                      if (!time12 || !time12.includes(" "))
                                        return 0;
                                      const parts = time12.split(" ");
                                      if (parts.length !== 2) return 0;
                                      const [time, period] = parts;
                                      const timeParts = time.split(":");
                                      if (timeParts.length !== 2) return 0;
                                      const hours = parseInt(timeParts[0]) || 0;
                                      const minutes =
                                        parseInt(timeParts[1]) || 0;
                                      let hour24 = hours;
                                      if (period === "PM" && hours !== 12)
                                        hour24 += 12;
                                      if (period === "AM" && hours === 12)
                                        hour24 = 0;
                                      return hour24 * 60 + minutes;
                                    };
                                    return (
                                      convertTo24Hour(a) - convertTo24Hour(b)
                                    );
                                  }
                                );

                                const totalHours = sortedHours.length * 0.5;
                                const startTime = sortedHours[0];

                                // Calculate the actual end time by adding 30 minutes to the last selected slot
                                const lastSlotTime =
                                  sortedHours[sortedHours.length - 1];

                                // Safely parse the last slot time
                                if (
                                  !lastSlotTime ||
                                  !lastSlotTime.includes(" ")
                                ) {
                                  return "Invalid time format";
                                }

                                const parts = lastSlotTime.split(" ");
                                if (parts.length !== 2) {
                                  return "Invalid time format";
                                }

                                const [time, period] = parts;
                                const timeParts = time.split(":");
                                if (timeParts.length !== 2) {
                                  return "Invalid time format";
                                }

                                const hours = parseInt(timeParts[0]) || 0;
                                const minutes = parseInt(timeParts[1]) || 0;
                                let hour24 = hours;
                                if (period === "PM" && hours !== 12)
                                  hour24 += 12;
                                if (period === "AM" && hours === 12) hour24 = 0;

                                // Add 30 minutes to get the end time
                                const endMinutes = hour24 * 60 + minutes + 30;
                                const endHour24 = Math.floor(endMinutes / 60);
                                const endMin = endMinutes % 60;

                                // Convert back to 12-hour format
                                const endPeriod = endHour24 >= 12 ? "PM" : "AM";
                                const endHour12 = endHour24 % 12 || 12;
                                const endTime = `${endHour12}:${endMin
                                  .toString()
                                  .padStart(2, "0")} ${endPeriod}`;

                                if (sortedHours.length === 1) {
                                  return `${startTime} - ${endTime} (0.5 hours)`;
                                } else {
                                  return `${startTime} - ${endTime} (${totalHours} ${
                                    totalHours === 1 ? "hour" : "hours"
                                  })`;
                                }
                              })()
                            : "No hours set"}
                        </div>
                      </div>
                    </div>
                    {isEditing && (
                      <div className="flex items-center space-x-2">
                        <button
                          className="rounded-lg border border-primaryBlue bg-white px-3 py-2 text-sm font-medium text-primaryBlue transition-colors hover:bg-blue-50"
                          onClick={() => {
                            setSelectedCoachForHours(coach);
                            setIsHoursModalOpen(true);
                          }}
                        >
                          {coach.hours.length > 0 ? "Edit hours" : "Set hours"}
                        </button>
                        <button
                          onClick={() => handleRemoveCoach(coach)}
                          className="rounded-lg border border-red-200 p-2 text-red-400 transition-colors hover:bg-red-50 hover:text-red-600"
                          title="Remove coach"
                        >
                          <XMarkIcon className="h-4 w-4" />
                        </button>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="rounded-lg border-2 border-dashed border-gray-200 p-6 text-center">
                <div className="mx-auto mb-3 flex h-12 w-12 items-center justify-center rounded-full bg-gray-100">
                  <svg
                    className="h-6 w-6 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z"
                    />
                  </svg>
                </div>
                <p className="font-medium text-gray-500">No coaches assigned</p>
                <p className="mt-1 text-sm text-gray-400">
                  {isEditing
                    ? "Click 'Add Coach' to assign coaches to this clinic"
                    : "Coaches will appear here when assigned to this clinic"}
                </p>
                {isEditing && (
                  <button
                    onClick={async () => {
                      await getAllCoaches(); // Refresh coaches before opening modal
                      setIsCoachModalOpen(true);
                    }}
                    disabled={isLoadingCoaches}
                    className="mt-4 inline-flex items-center rounded-lg bg-primaryBlue px-4 py-2 text-white transition-colors hover:bg-primaryBlue/90 disabled:cursor-not-allowed disabled:opacity-50"
                  >
                    {isLoadingCoaches ? (
                      <svg
                        className="mr-2 h-4 w-4 animate-spin"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                    ) : (
                      <svg
                        className="mr-2 h-4 w-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 4v16m8-8H4"
                        />
                      </svg>
                    )}
                    {isLoadingCoaches ? "Loading coaches..." : "Add coaches"}
                  </button>
                )}
              </div>
            )}
          </div>
        </div>
        {isLoading && <LoadingSpinner />}
      </RightSideModal>{" "}
      {/* Render the sport change modal when needed */}
      {showSportChangeModal && (
        <SportChangeModal
          onClose={() => {
            // Reset to original values
            setValue("sport_id", originalSportData.sport_id);
            setValue("type", originalSportData.type);
            setValue("sub_type", originalSportData.sub_type);

            // Reset states and close modal
            setSportChangeOption(null);
            setPendingFormData(null);
            setShowSportChangeModal(false);
          }}
          onConfirm={async (option) => {
            // Set the selected option
            const selectedOption = parseInt(option);
            setSportChangeOption(selectedOption);

            console.log("Sport change option selected:", selectedOption); // Debug log

            // Close the modal
            setShowSportChangeModal(false);

            // Proceed with save using the pending form data
            if (pendingFormData) {
              await performSave(pendingFormData);
            }

            // Reset pending form data
            setPendingFormData(null);
          }}
          clinic={defaultClinic}
        />
      )}
      {/* Coach Selection Modal */}
      {isCoachModalOpen &&
        (() => {
          const availableCoaches = allCoaches.filter(
            (coach) => !selectedCoaches.some((sc) => sc.id === coach.id)
          );
          console.log("CoachSelectionModal props:", {
            allCoaches,
            selectedCoaches,
            availableCoaches,
          });

          return (
            <CoachSelectionModal
              coaches={availableCoaches}
              selectedCoaches={tempSelectedCoaches}
              setSelectedCoaches={setTempSelectedCoaches}
              isOpen={isCoachModalOpen}
              loading={isAddingCoach}
              onClose={() => {
                resetCoachSelectionState();
              }}
              onSave={async () => {
                if (tempSelectedCoaches.length === 0) {
                  showToast(
                    globalDispatch,
                    "Please select at least one coach",
                    3000,
                    "warning"
                  );
                  return;
                }

                console.log(
                  "Setting pending coaches for hours:",
                  tempSelectedCoaches
                );

                // Start the hours selection process
                setPendingCoachesForHours(tempSelectedCoaches);
                setCurrentCoachIndex(0);
                setTempSelectedCoaches([]);
                setIsCoachModalOpen(false);

                // The useEffect will automatically trigger processCoachesWithHours
              }}
            />
          );
        })()}
      {/* Hours Selection Modal */}
      <HoursSelectionModal />
    </>
  );
};

export default ClinicDetailsModal;
